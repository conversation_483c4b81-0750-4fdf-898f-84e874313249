import { Box, Text } from '@adminjs/design-system';
import { ApiClient } from 'adminjs';
import React from 'react';
import { useState, useEffect } from 'react';
import { ThemeProvider } from 'styled-components';
import { theme } from '@adminjs/design-system';
import styled from 'styled-components';
import UserSearch from './components/UserSearch.js';
import UserInfoCard from './components/UserInfoCard.js';
import FilterPanelEnhancer from './components/FilterPanelEnhancer.js';

import ServicesList from './components/ServicesList.js';
import ServiceDetails from './components/ServiceDetails.js';
import SuperAPIInfo from './components/SuperAPIInfo.js';
import InvoicesList from './components/InvoicesList.js';
import EventsList from './components/EventsList.js';
import NotifyEmailsList from './components/NotifyEmailsList.js';
import WalletInfo from './components/WalletInfo.js';
import { User } from './types/index.js';

// AdminJS-style layout components
const DashboardContainer = styled(Box)`
  padding: 24px;

  @media (max-width: 768px) {
    padding: 16px;
  }
`;

const DashboardHeader = styled(Box)`
  margin-bottom: 24px;
`;

const GridContainer = styled(Box)`
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
`;

const TwoColumnGrid = styled(GridContainer)`
  grid-template-columns: 1fr 1fr;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
  }
`;

const ThreeColumnGrid = styled(GridContainer)`
  grid-template-columns: repeat(3, 1fr);

  @media (max-width: 1280px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SingleColumnGrid = styled(GridContainer)`
  grid-template-columns: 1fr;
`;



const Dashboard = () => {
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userServices, setUserServices] = useState([]);
  const [userInvoices, setUserInvoices] = useState([]);
  const [isLoadingServices, setIsLoadingServices] = useState(false);
  const [isLoadingInvoices, setIsLoadingInvoices] = useState(false);
  const [userWallet, setUserWallet] = useState(null);
  const [isLoadingWallet, setIsLoadingWallet] = useState(false);
  const [userEvents, setUserEvents] = useState([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [currentService, setCurrentService] = useState(null);
  const [isUpdatingService, setIsUpdatingService] = useState(false);
  const [products, setProducts] = useState([]);
  const [pricings, setPricings] = useState([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [userEmails, setUserEmails] = useState([]);
  const [isLoadingEmails, setIsLoadingEmails] = useState(false);
  const api = new ApiClient();
  const [superApiKey, setSuperApiKey] = useState(0);

  const fetchUserData = async (userId: string, email: string) => {
    // Fetch services
    setIsLoadingServices(true);
    try {
      const servicesResponse = await api.resourceAction({
        resourceId: 'Service',
        actionName: 'list',
        params: {
          filters: {
            userid: userId,
          },
        },
      });
      setUserServices(servicesResponse.data.records);
      if (servicesResponse.data.records.length === 1) {
        setCurrentService(servicesResponse.data.records[0]);
      }
    } catch (error) {
      console.error('Failed to fetch services:', error);
    } finally {
      setIsLoadingServices(false);
    }

    // Fetch invoices
    setIsLoadingInvoices(true);
    try {
      const invoicesResponse = await api.resourceAction({
        resourceId: 'Invoice',
        actionName: 'list',
        params: {
          filters: {
            userid: userId,
          },
          sortBy: 'duedate',
          direction: 'desc',
          page: 1,
          perPage: 5,
        },
      });
      setUserInvoices(invoicesResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch invoices:', error);
    } finally {
      setIsLoadingInvoices(false);
    }

    // Add wallet fetching
    setIsLoadingWallet(true);
    try {
      const walletResponse = await api.resourceAction({
        resourceId: 'Wallet',
        actionName: 'list',
        params: {
          filters: {
            userId: userId,
          },
        },
      });
      setUserWallet(walletResponse.data.records[0]); // Assuming one wallet per user
    } catch (error) {
      console.error('Failed to fetch wallet:', error);
    } finally {
      setIsLoadingWallet(false);
    }

    // Add events fetching
    setIsLoadingEvents(true);
    try {
      const eventsResponse = await api.resourceAction({
        resourceId: 'Event',
        actionName: 'list',
        params: {
          filters: {
            userId: userId,
          },
          page: 1,
          perPage: 5,
          sortBy: 'createdAt',
          direction: 'desc',
        },
      });
      setUserEvents(eventsResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch events:', error);
    } finally {
      setIsLoadingEvents(false);
    }

    // Add notify emails fetching
    setIsLoadingEmails(true);
    try {
      const emailsResponse = await api.resourceAction({
        resourceId: 'NotifyEmail',
        actionName: 'list',
        params: {
          filters: {
            receivers: email,
          },
          sortBy: 'createdAt',
          direction: 'desc',
          page: 1,
          perPage: 5,
        },
      });
      setUserEmails(emailsResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch notify emails:', error);
    } finally {
      setIsLoadingEmails(false);
    }
  };

  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    fetchUserData(user.id, user.params?.email || '');
  };

  const handleServiceSelect = (service: any) => {
    setCurrentService(service);
  };

  const handleServiceUpdate = async (updatedService: any) => {
    setIsUpdatingService(true);

    // Define the fields that are allowed to be edited
    const editableFields = ['domainstatus', 'packageid', 'nextduedate', 'billingcycle'];

    // Create an object with only the editable fields
    const updateData = editableFields.reduce((acc: any, field) => {
      if (updatedService.params[field] !== currentService?.params[field]) {
        acc[field] = updatedService.params[field];
      }
      return acc;
    }, {});

    // Only proceed if there are actual changes
    if (Object.keys(updateData).length === 0) {
      setIsUpdatingService(false);
      return;
    }

    try {
      const response = await api.recordAction({
        resourceId: 'Service',
        recordId: updatedService.id,
        actionName: 'edit',
        data: updateData,
      });
      setCurrentService(response.data.record);
      setSuperApiKey(prev => prev + 1);
    } catch (error) {
      console.error('Failed to update service:', error);
    } finally {
      setIsUpdatingService(false);
    }
  };



  const fetchProductsAndPricing = async () => {
    setIsLoadingProducts(true);
    try {
      // Fetch products
      const productsResponse = await api.resourceAction({
        resourceId: 'Product',
        actionName: 'list',
      });
      setProducts(productsResponse.data.records);

      // Fetch pricing
      const pricingResponse = await api.resourceAction({
        resourceId: 'Pricing',
        actionName: 'list',
      });
      setPricings(pricingResponse.data.records);
    } catch (error) {
      console.error('Failed to fetch products and pricing:', error);
    } finally {
      setIsLoadingProducts(false);
    }
  };

  useEffect(() => {
    fetchProductsAndPricing();
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <DashboardContainer>
        <DashboardHeader>
          <Text variant="h1" fontWeight="bold">
            Flash 管理系统
          </Text>
        </DashboardHeader>

        <UserSearch onUserSelect={handleUserSelect} />

        {/* User Details Row */}
        <SingleColumnGrid>
          <UserInfoCard selectedUser={selectedUser} />
        </SingleColumnGrid>

        {/* Services Row - Three Columns */}
        <ThreeColumnGrid>
          <ServicesList
            userServices={userServices}
            isLoadingServices={isLoadingServices}
            selectedUser={selectedUser}
            currentService={currentService}
            onServiceSelect={handleServiceSelect}
          />
          <ServiceDetails
            currentService={currentService}
            products={products}
            pricings={pricings}
            isLoadingProducts={isLoadingProducts}
            onServiceUpdate={handleServiceUpdate}
            isUpdatingService={isUpdatingService}
          />
          <SuperAPIInfo currentService={currentService} superApiKey={superApiKey} />
        </ThreeColumnGrid>

        {/* Invoices and Events Row */}
        <TwoColumnGrid>
          <InvoicesList
            userInvoices={userInvoices}
            isLoadingInvoices={isLoadingInvoices}
            selectedUser={selectedUser}
          />
          <EventsList
            userEvents={userEvents}
            isLoadingEvents={isLoadingEvents}
            selectedUser={selectedUser}
          />
        </TwoColumnGrid>

        {/* Notify Emails Row */}
        <SingleColumnGrid>
          <NotifyEmailsList
            userEmails={userEmails}
            isLoadingEmails={isLoadingEmails}
            selectedUser={selectedUser}
          />
        </SingleColumnGrid>

        {/* Wallet Row */}
        <SingleColumnGrid>
          <WalletInfo
            userWallet={userWallet}
            isLoadingWallet={isLoadingWallet}
            selectedUser={selectedUser}
          />
        </SingleColumnGrid>
      </DashboardContainer>

      {/* Global Filter Panel Enhancer */}
      <FilterPanelEnhancer />
    </ThemeProvider>
  );
};

export default Dashboard;
