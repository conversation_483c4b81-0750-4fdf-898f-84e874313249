import React, { useEffect } from 'react';

const FilterPanelEnhancer: React.FC = () => {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 更全面地查找筛选面板 - AdminJS 使用的可能的选择器
      const filterPanel = document.querySelector('[data-testid="filter-drawer"]') ||
                         document.querySelector('.filter-drawer') ||
                         document.querySelector('[class*="FilterDrawer"]') ||
                         document.querySelector('[class*="filter-drawer"]') ||
                         document.querySelector('aside[class*="drawer"]') ||
                         document.querySelector('div[class*="drawer"]') ||
                         document.querySelector('[role="dialog"]') ||
                         document.querySelector('.adminjs-drawer');

      if (filterPanel) {
        const target = event.target as Element;

        // 检查点击是否在筛选面板外部
        if (!filterPanel.contains(target)) {
          // 查找关闭按钮 - 尝试多种可能的选择器
          const closeButton = filterPanel.querySelector('button[aria-label*="close"]') ||
                             filterPanel.querySelector('button[aria-label*="Close"]') ||
                             filterPanel.querySelector('button[title*="close"]') ||
                             filterPanel.querySelector('button[title*="Close"]') ||
                             filterPanel.querySelector('button:first-child') ||
                             filterPanel.querySelector('svg[class*="close"]')?.closest('button') ||
                             filterPanel.querySelector('[class*="close"]') ||
                             document.querySelector('button[class*="close"]');

          if (closeButton) {
            // 模拟点击关闭按钮
            (closeButton as HTMLButtonElement).click();
          } else {
            // 如果找不到关闭按钮，尝试按 ESC 键
            const escEvent = new KeyboardEvent('keydown', {
              key: 'Escape',
              code: 'Escape',
              keyCode: 27,
              which: 27,
              bubbles: true
            });
            document.dispatchEvent(escEvent);
          }
        }
      }
    };

    // 延迟添加事件监听器，确保 DOM 已经渲染
    const timer = setTimeout(() => {
      document.addEventListener('click', handleClickOutside, true);
    }, 500);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('click', handleClickOutside, true);
    };
  }, []);

  // 这个组件不渲染任何内容，只是添加事件监听器
  return null;
};

export default FilterPanelEnhancer;
