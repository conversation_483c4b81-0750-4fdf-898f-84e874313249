import React, { useState } from 'react';
import { Box, Text, Button } from '@adminjs/design-system';
import styled from 'styled-components';
import UpdatePassword from './UpdatePassword.js';
import { User } from '../types/index.js';

const StyledUserInfoCard = styled(Box)`
  background: white;
  padding: 24px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
`;

const UserInfoHeader = styled(Box)`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
`;

const UserInfoGrid = styled(Box)`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 16px;
  }
`;

const InfoItem = styled(Box)`
  margin-bottom: 16px;
`;

interface UserInfoCardProps {
  selectedUser: User | null;
}

const UserInfoCard: React.FC<UserInfoCardProps> = ({ selectedUser }) => {
  const [showPasswordPopover, setShowPasswordPopover] = useState(false);

  return (
    <StyledUserInfoCard>
      <UserInfoHeader>
        <Text variant="lg" fontWeight="bold">
          Selected User Details
        </Text>
      </UserInfoHeader>

      {selectedUser ? (
        <UserInfoGrid>
          <Box>
            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">User ID</Text>
              <Text>{String(selectedUser.id || 'N/A')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Email Address</Text>
              <Text>{String(selectedUser.params?.email || 'N/A')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Signup Date</Text>
              <Text>
                {selectedUser.params?.createdAt
                  ? new Date(selectedUser.params.createdAt).toLocaleDateString()
                  : 'N/A'}
              </Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Email Verified</Text>
              <Text color={selectedUser.params?.emailVerified ? 'success' : 'error'}>
                {selectedUser.params?.emailVerified ? '✅ Verified' : '❌ Not Verified'}
              </Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Card Last Four</Text>
              <Text>{String(selectedUser.params?.cardLastFour || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Gateway ID</Text>
              <Text>{String(selectedUser.params?.gatewayId || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Last Login</Text>
              <Text>{String(selectedUser.params?.lastLogin || 'Never')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">IP Address</Text>
              <Text>{String(selectedUser.params?.ip || 'Not set')}</Text>
            </InfoItem>

            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Host</Text>
              <Text>{String(selectedUser.params?.host || 'Not set')}</Text>
            </InfoItem>
          </Box>
          <Box>
            <InfoItem>
              <Text fontWeight="bold" color="grey60" variant="sm">Quick Actions</Text>
              <Box marginTop="default" display="flex" flexWrap="wrap" style={{ gap: '10px' }}>
                <Button
                  onClick={() => window.location.href = `/admin/resources/User/records/${selectedUser.id}/show`}
                  variant="primary"
                  size="sm"
                >
                  View Details
                </Button>

                <Box position="relative">
                  <Button
                    onClick={() => setShowPasswordPopover(!showPasswordPopover)}
                    variant="secondary"
                    size="sm"
                  >
                    Change Password
                  </Button>
                  {showPasswordPopover && (
                    <Box
                      position="absolute"
                      top="100%"
                      left="0"
                      backgroundColor="white"
                      padding="lg"
                      boxShadow="cardHover"
                      borderRadius="default"
                      zIndex={1000}
                      marginTop="sm"
                      width="280px"
                      border="1px solid"
                      borderColor="grey20"
                    >
                      <UpdatePassword userId={selectedUser.id} />
                    </Box>
                  )}
                </Box>

                <Button
                  onClick={() => window.open(`https://web.flashgates.com/api/v1/users/userToken?token=CCAEStJC4HDt6Hdr58usCV3DMV&email=x&id=${selectedUser.id}`, '_blank')}
                  variant="secondary"
                  size="sm"
                >
                  Sign In As User
                </Button>
              </Box>
            </InfoItem>
          </Box>
        </UserInfoGrid>
      ) : (
        <Box textAlign="center" padding="xl">
          <Text color="grey60">
            Search for a user to see their details
          </Text>
        </Box>
      )}
    </StyledUserInfoCard>
  );
};

export default UserInfoCard;
