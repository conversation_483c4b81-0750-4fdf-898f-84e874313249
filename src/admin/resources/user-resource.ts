import { User } from "../models/user.entity.js";
import bcrypt from 'bcrypt';
import { Components } from '../component-loader.js';
import { DataSource } from 'typeorm';

export const UserResource = {
  resource: User,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    showProperties: [
      'id',
      'uuid',
      'email',
      'emailVerified',
      'status',
      'datecreated',
      'lastLogin',
      'createdAt',
      'updatedAt',
      'firstname',
      'lastname',
      'language',
      'notes',
      'marketingOptIn',
      'credit',
      'allowAppleIAP',
      'userRelatedData'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      uuid: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string'
      },
      email: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        isTitle: true,
        isRequired: true,
        custom: {
          validation: {
            required: 'Email is required',
            pattern: {
              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: 'Please enter a valid email address'
            }
          }
        },
        props: {
          placeholder: 'Enter email address'
        }
      },
      emailVerified: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        isRequired: true,
        availableValues: [
          { value: 'Active', label: 'Active' },
          { value: 'Inactive', label: 'Inactive' },
          { value: 'Closed', label: 'Closed' }
        ]
      },
      datecreated: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      },
      lastLogin: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'string'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime',
        components: {
          show: Components.DateDisplay,
          list: Components.DateDisplay
        }
      },
      updatedAt: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'datetime'
      },
      firstname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        custom: {
          validation: {
            required: 'First name is required'
          }
        },
        props: {
          placeholder: 'Enter first name'
        }
      },
      lastname: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        custom: {
          validation: {
            required: 'Last name is required'
          }
        },
        props: {
          placeholder: 'Enter last name'
        }
      },
       language: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      marketingOptIn: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      allowAppleIAP: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'boolean'
      },
      credit: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
       notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      password: {
        isVisible: { list: false, filter: false, show: false, edit: false },
        type: 'string'
      },
      userRelatedData: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.UserDetails
        }
      }
    },
    actions: {
      edit: {
        before: async (request, context) => {
          if (request.method === 'post' && request.payload) {
            // 定义允许编辑的字段
            const allowedFields = [
              'email',
              'firstname', 
              'lastname',
              'emailVerified',
              'status',
              'language',
              'notes',
              'marketingOptIn',
              'credit',
              'allowAppleIAP'
            ];
            
            // 创建一个新的payload，只包含允许的字段
            const cleanedPayload: any = {};
            let hasValidChanges = false;
            
            allowedFields.forEach(field => {
              if (request.payload[field] !== undefined) {
                cleanedPayload[field] = request.payload[field];
                hasValidChanges = true;
              }
            });
            
            // 自动更新时间戳
            cleanedPayload.updatedAt = new Date();
            
            // 数据类型转换处理
            if (cleanedPayload.emailVerified !== undefined) {
              cleanedPayload.emailVerified = parseInt(cleanedPayload.emailVerified) || 0;
            }
            if (cleanedPayload.credit !== undefined && typeof cleanedPayload.credit === 'string') {
              cleanedPayload.credit = parseFloat(cleanedPayload.credit) || 0;
            }
            
            // 使用清理后的payload
            request.payload = cleanedPayload;
          }
        
          return request;
        },
        after: async (response, request, context) => {
          // 更新完成后保留在当前编辑页面，而不是跳转到列表页面
          if (request.method === 'post' && response.record) {
            return {
              ...response,
              redirectUrl: null, // 阻止默认的跳转行为
              notice: {
                message: 'User information updated successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
      'update-password': {
        actionType: 'record',
        component: Components.UpdatePassword,
        handler: async (request, response, context) => {
          try {
            // 打印提交的数据（注意密码安全，不直接打印密码内容）
            if (request.payload) {
              const safePayload = { ...request.payload };
              if (safePayload.password) {
                safePayload.password = '*'.repeat(safePayload.password.length);
              }
            }
            
            const userId = context.record?.params?.id || 
                          request.params?.recordId;
            
            if (!userId) {
              throw new Error('User ID not found');
            }

            // If this is a GET request (initial page load), just return the record
            if (request.method === 'get' || !request.payload) {
              let recordJSON;
              
              if (context.record) {
                recordJSON = context.record.toJSON(context.currentAdmin);
              } else {
                // Create a minimal record structure when context.record is not available
                recordJSON = {
                  id: userId,
                  params: {
                    id: userId
                  },
                  populated: {},
                  errors: {}
                };
              }

              return {
                record: recordJSON
              };
            }

            // If this is a POST request (form submission), process the password update
            const { password } = request.payload || {};
            
            if (!password || password.length < 8) {
              throw new Error('Password must be at least 8 characters long');
            }

            const user = await context.resource.findOne(userId);
            if (!user) {
              throw new Error('User not found');
            }
            // Hash password
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);

            // Update user password
            await context.resource.update(userId, {
              password: hashedPassword
            });

            return {
              record: context.record?.toJSON(context.currentAdmin),
              notice: {
                message: 'Password successfully updated',
                type: 'success'
              },
              redirectUrl: null // 阻止跳转，保留在当前页面
            };
          } catch (error) {
            console.error('=== 密码更新操作失败 ===');
            console.error('错误详情:', error.message);
            console.error('错误堆栈:', error.stack);
            throw error;
          }
        },
      },
      list: {
        before: async (request, context) => {
          // 在列表视图中也添加调试信息
          if (request.query && request.query.filters) {
            console.log('=== 列表查询过滤器 ===', request.query.filters);
          }
          return request;
        }
      },
    }
  }
};

