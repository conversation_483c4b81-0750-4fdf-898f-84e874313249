import { Invoice } from "../models/invoice.entity.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

export const InvoiceResource: ResourceWithOptions = {
  resource: Invoice,
  options: {
    // 自定义列表页面字段显示顺序
    listProperties: [
      'id',
      'userid',
      'date',
      'duedate',
      'status',
      'total',
      'paymentmethod',
      'datepaid',
      'createdAt'
    ],
    // 自定义详情页面字段显示顺序
    showProperties: [
      'id',
      'userid',
      'status',
      'date',
      'duedate',
      'datepaid',
      'paymentmethod',
      'total',
      'subtotal',
      'tax',
      'tax2',
      'taxrate',
      'taxrate2',
      'credit',
      'notes',
      'createdAt',
      'updatedAt',
      'invoiceItems'
    ],
    // 自定义新建页面字段显示顺序（包含 datepaid，用于创建已支付订单）
    editProperties: [
      'userid',
      'status',
      'date',
      'duedate',
      'datepaid',
      'paymentmethod',
      'total',
      'subtotal',
      'tax',
      'tax2',
      'taxrate',
      'taxrate2',
      'credit',
      'notes'
    ],
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number',
        isTitle: true
      },
      userid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User',
        isRequired: true
      },
      invoicenum: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
      },
      date: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date',
        isRequired: true
      },
      duedate: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date',
        isRequired: true
      },
      datepaid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date',
        components: {
          list: Components.DatePaidDisplay,
          show: Components.DatePaidDisplay
        }
      },
      status: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        isRequired: true,
        availableValues: [
          { value: 'Unpaid', label: 'Unpaid' },
          { value: 'Paid', label: 'Paid' },
          { value: 'Cancelled', label: 'Cancelled' },
          { value: 'Refunded', label: 'Refunded' },
          { value: 'Collections', label: 'Collections' },
          { value: 'Payment Pending', label: 'Payment Pending' }
        ]
      },
      paymentmethod: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      total: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number',
        isRequired: true
      },
      subtotal: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      tax: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      tax2: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      taxrate: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      taxrate2: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      credit: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'number'
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      },
      createdAt: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'datetime'
      },
      updatedAt: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'datetime'
      },
      invoiceItems: {
        isVisible: { list: false, filter: false, show: true, edit: false },
        type: 'string',
        components: {
          show: Components.InvoiceItems
        }
      }
    },
    actions: {
      new: {
        before: async (request: any) => {
          // 设置默认值
          if (request.payload) {
            // 如果没有设置日期，使用当前日期
            if (!request.payload.date) {
              request.payload.date = new Date().toISOString().split('T')[0];
            }

            // 如果没有设置到期日期，默认为30天后
            if (!request.payload.duedate) {
              const dueDate = new Date();
              dueDate.setDate(dueDate.getDate() + 30);
              request.payload.duedate = dueDate.toISOString().split('T')[0];
            }

            // 如果没有设置状态，默认为 Unpaid
            if (!request.payload.status) {
              request.payload.status = 'Unpaid';
            }

            // 如果没有设置总金额，默认为 0
            if (!request.payload.total) {
              request.payload.total = 0;
            }

            // 重要：智能处理 datepaid 字段以符合 WHMCS 标准
            // WHMCS 的 datepaid 字段不允许 NULL，需要设置有效的默认值
            if (request.payload.status === 'Paid') {
              // 如果状态是 Paid 但没有设置 datepaid，使用当前日期
              if (!request.payload.datepaid) {
                request.payload.datepaid = new Date().toISOString().split('T')[0];
              }
              // 如果用户手动设置了 datepaid，保持用户的选择
            } else {
              // 对于非 Paid 状态，只有在用户没有手动设置 datepaid 时才使用默认值
              if (!request.payload.datepaid) {
                // 设置为默认的"未支付"日期（1970-01-01）
                // 这是 WHMCS 的标准做法，表示未支付
                request.payload.datepaid = '1970-01-01';
              }
              // 如果用户手动设置了 datepaid（比如预设支付日期），保持用户的选择
            }

            // 设置其他必要的默认值
            if (!request.payload.paymentmethod) {
              request.payload.paymentmethod = '';
            }

            if (!request.payload.invoicenum) {
              // 生成简单的发票号码
              const timestamp = Date.now();
              request.payload.invoicenum = `INV-${timestamp}`;
            }

            // 设置其他数值字段的默认值
            if (request.payload.subtotal === undefined) {
              request.payload.subtotal = 0;
            }
            if (request.payload.tax === undefined) {
              request.payload.tax = 0;
            }
            if (request.payload.tax2 === undefined) {
              request.payload.tax2 = 0;
            }
            if (request.payload.taxrate === undefined) {
              request.payload.taxrate = 0;
            }
            if (request.payload.taxrate2 === undefined) {
              request.payload.taxrate2 = 0;
            }
            if (request.payload.credit === undefined) {
              request.payload.credit = 0;
            }
            if (!request.payload.notes) {
              request.payload.notes = '';
            }
          }

          return request;
        },
        after: async (response: any, request: any) => {
          // 创建成功后的处理
          if (response.record) {
            return {
              ...response,
              notice: {
                message: 'Invoice created successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
      edit: {
        after: async (response: any, request: any) => {
          // 阻止跳转到列表页面，保留在当前编辑页面
          if (request.method === 'post' && response.record) {
            return {
              ...response,
              redirectUrl: null,
              notice: {
                message: 'Invoice information updated successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
    }
  }
};
