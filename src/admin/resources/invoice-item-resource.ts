import { InvoiceItem } from "../models/invoice.entity.js";
import { ResourceWithOptions } from "adminjs";
import { Components } from '../component-loader.js';

export const InvoiceItemResource: ResourceWithOptions = {
  resource: InvoiceItem,
  options: {
    // 默认排序：ID 降序
    sort: {
      sortBy: 'id',
      direction: 'desc'
    },
    properties: {
      id: {
        isVisible: { list: true, filter: true, show: true, edit: false },
        type: 'number'
      },
      invoiceid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'Invoice'
      },
      userid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'User',
        components: {
          list: Components.UserReference,
          show: Components.UserReference
        }
      },
      relid: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'reference',
        reference: 'Service',
        components: {
          list: Components.ServiceReference,
          show: Components.ServiceReference
        }
      },
      type: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string',
        availableValues: [
          { value: 'Hosting', label: 'Hosting' },
          { value: 'Upgrade', label: 'Upgrade' },
          { value: 'Flow', label: 'Flow' },
          { value: 'Balance', label: 'Balance' },
          { value: 'Trial', label: 'Trial' },
          { value: 'Other', label: 'Other' }
        ]
      },
      description: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'string'
      },
      amount: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'number'
      },
      duedate: {
        isVisible: { list: true, filter: true, show: true, edit: true },
        type: 'date'
      },
      paymentmethod: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'string'
      },
      taxed: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'boolean'
      },
      notes: {
        isVisible: { list: false, filter: false, show: true, edit: true },
        type: 'textarea'
      }
    },
    actions: {
      edit: {
        after: async (response, request, context) => {
          // 阻止跳转到列表页面，保留在当前编辑页面
          if (request.method === 'post' && response.record) {
            return {
              ...response,
              redirectUrl: null,
              notice: {
                message: 'Invoice item updated successfully',
                type: 'success'
              }
            };
          }
          return response;
        },
      },
    }
  }
};
