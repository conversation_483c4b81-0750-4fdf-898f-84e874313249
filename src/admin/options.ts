import { AdminJSOptions } from 'adminjs';

import { componentLoader, Components } from './component-loader.js';
import { User } from './models/user.entity.js';
import { Product } from './models/product.entity.js';
import { Service} from './models/service.entity.js';
import { Pricing } from './models/pricing.entity.js';
import { Invoice } from './models/invoice.entity.js';
import { InvoiceItem } from './models/invoice.entity.js';
import { Wallet } from './models/wallet.entity.js';
import { Event } from './models/events.entity.js';
import { ServiceResource } from './resources/service-resource.js';
import { UserResource } from './resources/user-resource.js';
import { InvoiceResource } from './resources/invoice-resource.js';
import { InvoiceItemResource } from './resources/invoice-item-resource.js';
import { NotifyEmail } from './models/notify-email.entity.js';

const options: AdminJSOptions = {
  componentLoader,
  dashboard: { component: Components.Dashboard },
  rootPath: '/admin',
  branding: {
    companyName: 'FlashVPN管理系统',
    logo: '/assets/logo.svg',
    withMadeWithLove: false,
    theme: {
      colors: {
        primary100: '#667eea',
        primary80: '#7c3aed',
        primary60: '#8b5cf6',
        primary40: '#a78bfa',
        primary20: '#c4b5fd',
        grey100: '#151515',
        grey80: '#333333',
        grey60: '#4d4d4d',
        grey40: '#999999',
        grey20: '#cccccc',
        grey0: '#ffffff',
      }
    }
  },
  resources: [
    UserResource,
    Product,
    Pricing,
    ServiceResource,
    InvoiceResource,
    InvoiceItemResource,
    Wallet,
    Event,
    NotifyEmail
  ],
  databases: [],
  locale: {
    language: 'en',
    translations: {
      en: {
        labels: {
          User: 'Users',
          Product: 'Products',
          Pricing: 'Pricing',
          Service: 'Services',
          Invoice: 'Invoices',
          InvoiceItem: 'Invoice Items',
          Wallet: 'Wallets',
          Event: 'Events',
          NotifyEmail: 'Notify Emails',
          whmcs: 'WHMCS',
          api: 'API',
          notify: 'Notify',
          Whmcs: 'WHMCS',
          Api: 'API',
          Notify: 'Notify'
        },
        resources: {
          User: {
            name: 'Users',
            properties: {
              id: 'ID',
              email: 'Email',
              firstname: 'First Name',
              lastname: 'Last Name',
              emailVerified: 'Email Verified',
              status: 'Status',
              datecreated: 'Date Created',
              createdAt: 'Created At',
              updatedAt: 'Updated At',
              cardLastFour: 'Card Last Four',
              gatewayId: 'Gateway ID',
              lastLogin: 'Last Login',
              ip: 'IP Address',
              host: 'Host',
              pwResetKey: 'Password Reset Key',
              pwResetExpiry: 'Password Reset Expiry',
              emailOptOut: 'Email Opt Out',
              overrideAutoClose: 'Override Auto Close',
              allowSso: 'Allow SSO',
              emailPreferences: 'Email Preferences',
              billingCid: 'Billing CID'
            }
          },
          Product: {
            name: 'Products'
          },
          Pricing: {
            name: 'Pricing'
          },
          Service: {
            name: 'Services'
          },
          Invoice: {
            name: 'Invoices'
          },
          InvoiceItem: {
            name: 'Invoice Items'
          },
          Wallet: {
            name: 'Wallets'
          },
          Event: {
            name: 'Events'
          },
          NotifyEmail: {
            name: 'Notify Emails'
          }
        }
      }
    }
  }
};

export default options;
